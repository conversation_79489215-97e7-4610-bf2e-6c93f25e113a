apiVersion: v1
kind: ConfigMap
metadata:
  name: pure-heart-frontend-guest-config
  labels:
    app: pure-heart-frontend-guest
    app.kubernetes.io/name: pure-heart-frontend-guest
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: pure-heart-frontend-guest
    app.kubernetes.io/version: "4a061f0f"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "pure-heart-frontend-guest"
  PROJECT_ID: "pure-heart-frontend-guest"
  APPLICATION_TYPE: "react-frontend"
  SOURCE_REPO: "ChidhagniConsulting/Pure-Heart-Frontend-Guest"
  SOURCE_BRANCH: "9/merge"
  COMMIT_SHA: "4a061f0f"

  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:3003"
  API_URL: "http://localhost:3003/api"
  
  # Common Backend Configuration
  SERVER_PORT: "3003"

  NODE_ENV: "dev"
  PORT: "3003"

  

  

  

  
  # React Frontend Configuration
  REACT_APP_API_URL: "http://localhost:3003/api"
  REACT_APP_ENVIRONMENT: "dev"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "My App"
  PUBLIC_URL: "/"
  GENERATE_SOURCEMAP: "true"
  
