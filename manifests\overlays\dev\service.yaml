apiVersion: v1
kind: Service
metadata:
  name: PLACEHOLDER_APP_NAME-service-dev
  labels:
    app: PLACEH<PERSON>DER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/version: PLACEH<PERSON>DER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
spec:
  type: ClusterIP
  ports:
  - port: PLACEHOLDER_CONTAINER_PORT
    targetPort: PLACEHOLDER_CONTAINER_PORT
    protocol: TCP
    name: http
  selector:
    app: PLACEHOLDER_PROJECT_ID
