apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: pure-heart-frontend-guest-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: pure-heart-frontend-guest-project
    app.kubernetes.io/part-of: pure-heart-frontend-guest
    app.kubernetes.io/component: argocd-project
    app.kubernetes.io/version: "4a061f0f"
    app.kubernetes.io/managed-by: argocd
    source.repo: ChidhagniConsulting-Pure-Heart-Frontend-Guest
    source.branch: 9-merge
spec:
  description: "ArgoCD project for pure-heart-frontend-guest (react-frontend)"

  # Source repositories
  sourceRepos:
  - 'https://github.com/ChidhagniConsulting/Pure-Heart-Frontend-Guest'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps.git'
  
  # Destination clusters and namespaces
  destinations:
  # Local cluster
  - namespace: '*'
    server: https://kubernetes.default.svc
  # DigitalOcean cluster
  - namespace: '*'
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
  # Allow any cluster
  - namespace: '*'
    server: '*'
  
  # Cluster resource whitelist
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  - group: 'rbac.authorization.k8s.io'
    kind: ClusterRole
  - group: 'rbac.authorization.k8s.io'
    kind: ClusterRoleBinding
  - group: 'apiextensions.k8s.io'
    kind: CustomResourceDefinition
  - group: 'admissionregistration.k8s.io'
    kind: MutatingWebhookConfiguration
  - group: 'admissionregistration.k8s.io'
    kind: ValidatingWebhookConfiguration
  
  # Namespace resource whitelist
  namespaceResourceWhitelist:
  - group: ''
    kind: '*'
  - group: 'apps'
    kind: '*'
  - group: 'extensions'
    kind: '*'
  - group: 'networking.k8s.io'
    kind: '*'
  - group: 'policy'
    kind: '*'
  - group: 'rbac.authorization.k8s.io'
    kind: '*'
  - group: 'autoscaling'
    kind: '*'
  - group: 'batch'
    kind: '*'
  - group: 'external-secrets.io'
    kind: '*'
  - group: 'monitoring.coreos.com'
    kind: '*'
  
  # RBAC configuration
  roles:
  - name: admin
    description: "Admin access to the project"
    policies:
    - p, proj:pure-heart-frontend-guest-project:admin, applications, *, pure-heart-frontend-guest-project/*, allow
    - p, proj:pure-heart-frontend-guest-project:admin, repositories, *, *, allow
    - p, proj:pure-heart-frontend-guest-project:admin, clusters, *, *, allow
    groups:
    - argocd-admins

  - name: developer
    description: "Developer access to the project"
    policies:
    - p, proj:pure-heart-frontend-guest-project:developer, applications, get, pure-heart-frontend-guest-project/*, allow
    - p, proj:pure-heart-frontend-guest-project:developer, applications, sync, pure-heart-frontend-guest-project/*, allow
    - p, proj:pure-heart-frontend-guest-project:developer, applications, action/*, pure-heart-frontend-guest-project/*, allow
    - p, proj:pure-heart-frontend-guest-project:developer, repositories, get, *, allow
    groups:
    - argocd-developers
  
  # Sync windows (optional)
  syncWindows:
  - kind: allow
    schedule: '* * * * *'
    duration: 24h
    applications:
    - '*'
    manualSync: true
  
  # Signature keys (optional)
  signatureKeys: []
  
  # Orphaned resources (optional)
  orphanedResources:
    warn: true
