# Application Types Examples

This document provides complete examples for deploying React Frontend and Spring Boot Backend applications using the GitOps automation system.

## React Frontend Example

### GitHub Issue Template

When creating a GitHub Issue for a React Frontend application:

```yaml
Application Name: My React Dashboard
Project Identifier: react-dashboard
Container Image: myorg/react-dashboard:v2.1.0
Environment: production
Application Type: react-frontend
Container Port: 80
Health Check Path: /
Enable PostgreSQL Database: [ ] (unchecked - automatically disabled for frontend apps)
Enable Ingress: [x] (checked)
Ingress Host: dashboard.example.com
```

> **Note**: PostgreSQL database is automatically disabled for `react-frontend` and `web-app` application types since frontend applications typically don't require database connections.

### Repository Dispatch Payload

```json
{
  "app_name": "My React Dashboard",
  "project_id": "react-dashboard",
  "application_type": "react-frontend",
  "environment": "production",
  "docker_image": "myorg/react-dashboard",
  "docker_tag": "v2.1.0",
  "host_name": "devapi.pheart.in",
  "source_repo": "myorg/react-dashboard",
  "source_branch": "main",
  "commit_sha": "abc123def456"
}
```

**Note**: 
- `health_check_path` (/) and `container_port` (3000) are automatically determined from application type and added to secrets
- No need to specify these values in the payload - they're handled securely

### Generated Manifests

**ConfigMap (react-dashboard/k8s/configmap.yaml):**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: react-dashboard-config
  namespace: react-dashboard
  labels:
    app: react-dashboard
    component: config
    environment: production
    app-type: react-frontend
data:
  NODE_ENV: "production"
  PORT: "80"
  
  # React Frontend Configuration
  REACT_APP_API_URL: "http://localhost:8080"
  REACT_APP_ENVIRONMENT: "production"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "My React Dashboard"
  PUBLIC_URL: "/"
  GENERATE_SOURCEMAP: "false"
```

**Deployment (react-dashboard/k8s/deployment.yaml):**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: react-dashboard
  namespace: react-dashboard
spec:
  replicas: 2
  template:
    spec:
      # React Frontend - No init containers needed (stateless)
      containers:
      - name: react-dashboard
        image: myorg/react-dashboard:v2.1.0
        ports:
        - containerPort: 80
          name: http
        envFrom:
        - configMapRef:
            name: react-dashboard-config
        # React Frontend - Minimal environment variables
        env: []
        # Health Checks - Application Type Specific
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
```

## Spring Boot Backend Example

### GitHub Issue Template

When creating a GitHub Issue for a Spring Boot Backend application:

```yaml
Application Name: User Authentication API
Project Identifier: auth-api
Container Image: myorg/auth-api:v1.5.2
Environment: production
Application Type: springboot-backend
Container Port: 8080
Health Check Path: /actuator/health
Enable PostgreSQL Database: [x] (checked)
Database Name: userauth
Additional Secret Keys:
JWT_SECRET=supersecretkey
DB_PASSWORD=password
SMTP_USER=<EMAIL>
SMTP_PASS=fqactehafmzlltzz
GOOGLE_CLIENT_ID=1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
```

### Repository Dispatch Payload

```json
{
  "app_name": "User Authentication API",
  "project_id": "auth-api",
  "application_type": "springboot-backend",
  "environment": "production",
  "docker_image": "myorg/auth-api",
  "docker_tag": "v1.5.2",
  "host_name": "devapi.pheart.in",
  "source_repo": "myorg/auth-api",
  "source_branch": "main",
  "commit_sha": "def456ghi789"
}
```

**Note**: 
- `health_check_path` (/actuator/health) and `container_port` (8080) are automatically determined from application type and added to secrets
- No need to specify these values in the payload - they're handled securely

### Generated Manifests

**ConfigMap (auth-api/k8s/configmap.yaml):**
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-api-config
  namespace: auth-api
  labels:
    app: auth-api
    component: config
    environment: production
    app-type: springboot-backend
data:
  NODE_ENV: "production"
  PORT: "8080"
  
  # Spring Boot Backend Configuration
  SPRING_PROFILES_ACTIVE: "production"
  SERVER_PORT: "8080"
  SPRING_APPLICATION_NAME: "auth-api"
  
  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"
  
  # JVM Configuration
  JAVA_OPTS: "-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport"
  
  # Spring Boot Database Configuration
  SPRING_DATASOURCE_URL: "*************************************************"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "validate"
  SPRING_JPA_SHOW_SQL: "false"
  
  # Spring Boot Security & JWT
  JWT_EXPIRATION: "86400000"
  
  # Spring Boot CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://localhost:3001"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"
  
  # Spring Boot Mail Configuration
  SPRING_MAIL_HOST: "smtp.gmail.com"
  SPRING_MAIL_PORT: "587"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_AUTH: "true"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_ENABLE: "true"
```

**Deployment (auth-api/k8s/deployment.yaml):**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-api
  namespace: auth-api
spec:
  replicas: 3
  template:
    spec:
      # Backend Applications - Database init container
      initContainers:
      - name: wait-for-postgres
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h auth-api-postgres -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: DB_PASSWORD
      containers:
      - name: auth-api
        image: myorg/auth-api:v1.5.2
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: auth-api-config
        # Backend Applications - Full secret environment variables
        env:
        # Spring Boot specific database configuration
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: DB_USER
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: DB_PASSWORD
        - name: SPRING_MAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: SMTP_USER
        - name: SPRING_MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: SMTP_PASS
        # Common backend secrets
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-api-secrets
              key: JWT_SECRET
        # Health Checks - Spring Boot Backend
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 90
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
```

## CI/CD Integration Examples

### React Frontend CI/CD

```yaml
# .github/workflows/deploy.yml in React app repository
name: Deploy React App
on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Build and Push Docker Image
        run: |
          docker build -t myorg/react-dashboard:${{ github.sha }} .
          docker push myorg/react-dashboard:${{ github.sha }}
      
      - name: Deploy to GitOps
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "React Dashboard",
              "project_id": "react-dashboard",
              "application_type": "react-frontend",
              "environment": "production",
              "docker_image": "myorg/react-dashboard",
              "docker_tag": "${{ github.sha }}",
              "source_repo": "${{ github.repository }}",
              "source_branch": "${{ github.ref_name }}",
              "commit_sha": "${{ github.sha }}"
            }
```

### Spring Boot Backend CI/CD

```yaml
# .github/workflows/deploy.yml in Spring Boot app repository
name: Deploy Spring Boot API
on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up JDK 11
        uses: actions/setup-java@v3
        with:
          java-version: '11'
          distribution: 'temurin'
      
      - name: Build with Maven
        run: mvn clean package -DskipTests
      
      - name: Build and Push Docker Image
        run: |
          docker build -t myorg/auth-api:${{ github.sha }} .
          docker push myorg/auth-api:${{ github.sha }}
      
      - name: Deploy to GitOps
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "Auth API",
              "project_id": "auth-api",
              "application_type": "springboot-backend",
              "environment": "production",
              "docker_image": "myorg/auth-api",
              "docker_tag": "${{ github.sha }}",
              "source_repo": "${{ github.repository }}",
              "source_branch": "${{ github.ref_name }}",
              "commit_sha": "${{ github.sha }}"
            }
```

## Validation Commands

```bash
# Validate React Frontend deployment
kubectl get pods -n react-dashboard
kubectl logs -n react-dashboard deployment/react-dashboard
curl http://dashboard.example.com/

# Validate Spring Boot Backend deployment
kubectl get pods -n auth-api
kubectl logs -n auth-api deployment/auth-api
curl http://auth-api.example.com/actuator/health
```
