apiVersion: v1
kind: Secret
metadata:
  name: pure-heart-frontend-guest-secrets
  labels:
    app: pure-heart-frontend-guest
    app.kubernetes.io/name: pure-heart-frontend-guest
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: pure-heart-frontend-guest
    app.kubernetes.io/version: "4a061f0f"
    app.kubernetes.io/managed-by: argocd
    environment: dev
type: Opaque
data:
  # Development Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  

  

  

  

  

  

  # Development-specific secrets
  # DEBUG_MODE: DYNAMIC_DEBUG_MODE_B64
  # LOG_LEVEL: DYNAMIC_LOG_LEVEL_B64
