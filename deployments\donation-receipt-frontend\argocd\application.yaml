apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: donation-receipt-frontend-dev
  namespace: argocd
  labels:
    app.kubernetes.io/name: donation-receipt-frontend-dev
    app.kubernetes.io/part-of: donation-receipt-frontend
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/version: "0b08fe33"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    app-type: react-frontend
    source.repo: ChidhagniConsulting-Donation-Receipt-Frontend-Application
    source.branch: 48-merge
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: donation-receipt-frontend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/donation-receipt-frontend/overlays/dev
  destination:
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
    namespace: donation-receipt-frontend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Development environment for donation-receipt-frontend"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/Donation-Receipt-Frontend-Application"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "react-frontend"
  - name: Source Branch
    value: "48/merge"
  - name: Commit SHA
    value: "0b08fe33"
  - name: Configuration
    value: "Development configuration with debug enabled"
