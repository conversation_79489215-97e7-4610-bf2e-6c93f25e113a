apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: PLACEHOLDER_PROJECT_ID-ingress
  labels:
    app: PLACEHOLDER_PROJECT_ID
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - PLACEHOLDER_HOST
    secretName: PLACEHOLDER_PROJECT_ID-tls
  rules:
  - host: PLACEHOLDER_HOST
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: PLACEH<PERSON>DER_APP_NAME-service
            port:
              number: PLACEH<PERSON>DER_CONTAINER_PORT
