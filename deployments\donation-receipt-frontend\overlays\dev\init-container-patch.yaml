apiVersion: apps/v1
kind: Deployment
metadata:
  name: donation-receipt-frontend
  labels:
    app: donation-receipt-frontend
    app.kubernetes.io/name: donation-receipt-frontend
    app.kubernetes.io/component: database-init
    app.kubernetes.io/part-of: donation-receipt-frontend
    app.kubernetes.io/version: "0b08fe33"
    app.kubernetes.io/managed-by: argocd
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-database
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          # Database connection details are already decoded by Kubernetes from secrets
          # No need for base64 decoding as Kubernetes handles this automatically
          echo "🔍 Checking database connectivity..."
          echo "Database Host: $DB_HOST"
          echo "Database Port: $DB_PORT"
          echo "Database User: $DB_USER"
          echo "Database Name: $DB_NAME"

          # Set timeout for database connection attempts (10 minutes)
          TIMEOUT=600
          ELAPSED=0

          # Wait for database to be ready with timeout
          until pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -t 10; do
            if [ $ELAPSED -ge $TIMEOUT ]; then
              echo "❌ Database connection timeout after ${TIMEOUT} seconds"
              echo "🔍 Troubleshooting information:"
              echo "  - Check if database host is reachable: $DB_HOST:$DB_PORT"
              echo "  - Verify database credentials are correct"
              echo "  - Ensure database SSL configuration matches requirements"
              echo "  - Check network connectivity and firewall rules"
              echo "  - Database SSL Mode: $PGSSLMODE"
              # Try basic connectivity test
              echo "🔍 Testing basic connectivity..."
              nc -z "$DB_HOST" "$DB_PORT" && echo "✅ Port is reachable" || echo "❌ Port is not reachable"
              exit 1
            fi
            echo "⏳ Waiting for database to be ready... (${ELAPSED}s/${TIMEOUT}s)"
            sleep 10
            ELAPSED=$((ELAPSED + 10))
          done

          echo "✅ Database is ready!"

          # Optional: Test database connection
          if [ "$TEST_CONNECTION" = "true" ]; then
            echo "🧪 Testing database connection..."
            if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
              echo "✅ Database connection test successful!"
            else
              echo "❌ Database connection test failed!"
              echo "🔍 Connection test failed. This could be due to:"
              echo "  - Incorrect database credentials"
              echo "  - Database does not exist: $DB_NAME"
              echo "  - User does not have access to database"
              echo "  - SSL/TLS configuration mismatch"
              exit 1
            fi
          fi

          echo "🎉 Database initialization check completed successfully!"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        env:
        # Application metadata
        - name: APP_NAME
          value: "donation-receipt-frontend"
        - name: PROJECT_ID
          value: "donation-receipt-frontend"
        - name: APPLICATION_TYPE
          value: "react-frontend"
        - name: ENVIRONMENT
          value: "dev"
        # Database connection details from secrets
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_NAME
        - name: DB_SSL_MODE
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_SSL_MODE
        # PostgreSQL environment variables for pg_isready and psql
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_USER
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_PASSWORD
        - name: PGHOST
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_HOST
        - name: PGPORT
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_PORT
        - name: PGDATABASE
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_NAME
        - name: PGSSLMODE
          valueFrom:
            secretKeyRef:
              name: donation-receipt-frontend-secrets
              key: DB_SSL_MODE
        # Enable connection testing in dev environment
        - name: TEST_CONNECTION
          value: "true"
