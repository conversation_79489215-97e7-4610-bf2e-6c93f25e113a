apiVersion: v1
kind: ConfigMap
metadata:
  name: donation-receipt-frontend-config
  labels:
    app: donation-receipt-frontend
    app.kubernetes.io/name: donation-receipt-frontend
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: donation-receipt-frontend
    app.kubernetes.io/version: "0b08fe33"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "donation-receipt-frontend"
  PROJECT_ID: "donation-receipt-frontend"
  APPLICATION_TYPE: "react-frontend"
  SOURCE_REPO: "ChidhagniConsulting/Donation-Receipt-Frontend-Application"
  SOURCE_BRANCH: "48/merge"
  COMMIT_SHA: "0b08fe33"

  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:3000"
  API_URL: "http://localhost:3000/api"
  
  # Common Backend Configuration
  SERVER_PORT: "3000"

  NODE_ENV: "dev"
  PORT: "3000"

  

  

  

  
  # React Frontend Configuration
  REACT_APP_API_URL: "http://localhost:3000/api"
  REACT_APP_ENVIRONMENT: "dev"
  REACT_APP_VERSION: "1.0.0"
  REACT_APP_TITLE: "My App"
  PUBLIC_URL: "/"
  GENERATE_SOURCEMAP: "true"
  
