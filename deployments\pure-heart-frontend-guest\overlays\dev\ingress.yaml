apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: pure-heart-frontend-guest-ingress
  labels:
    app: pure-heart-frontend-guest
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - dev.pheart.in
    secretName: pure-heart-frontend-guest-tls
  rules:
  - host: dev.pheart.in
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: pure-heart-frontend-guest-service-dev
            port:
              number: 3003
