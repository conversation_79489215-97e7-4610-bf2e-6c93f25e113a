# Cross-Cluster ArgoCD Setup Guide

This guide explains how to configure ArgoCD for cross-cluster deployment where ArgoCD runs on one cluster and deploys applications to another cluster.

## Architecture Overview

```
┌─────────────────────────────────────┐    ┌─────────────────────────────────────┐
│        Management Cluster          │    │         Target Cluster             │
│   (158b6a47-3e7e-4dca-af0f-05a6e)  │    │   (6be4e15d-52f9-431d-84ec-ec8c)   │
│                                     │    │                                     │
│  ┌─────────────┐  ┌───────────────┐ │    │  ┌─────────────┐  ┌─────────────┐  │
│  │   ArgoCD    │  │ GitHub Actions│ │    │  │ Applications│  │ Workloads   │  │
│  │   Server    │  │   Runners     │ │    │  │ (dev/staging│  │             │  │
│  └─────────────┘  └───────────────┘ │    │  │ environments)│  │             │  │
│                                     │    │  └─────────────┘  └─────────────┘  │
└─────────────────────────────────────┘    └─────────────────────────────────────┘
                    │                                        ▲
                    │                                        │
                    └────────── Deploys to ─────────────────┘
```

## Current Configuration

### Cluster Mapping
- **Management Cluster**: `ca0e9f31-fd81-43a8-bace-ef88bb156117`
  - Runs ArgoCD server
  - Runs GitHub Actions self-hosted runners
  - Hosts production applications

- **Target Cluster**: `0eae25c8-1244-4c33-89fb-5e03974780a6`
  - Hosts dev and staging applications
  - Managed by <PERSON>rgoCD from management cluster

### Environment Mapping
- `dev` → Target Cluster (`0eae25c8-1244-4c33-89fb-5e03974780a6`)
- `staging` → Target Cluster (`0eae25c8-1244-4c33-89fb-5e03974780a6`)
- `production` → Management Cluster (`ca0e9f31-fd81-43a8-bace-ef88bb156117`)

## Setup Steps

### 1. Add Target Cluster to ArgoCD

Run the setup script to register the target cluster with ArgoCD:

```powershell
# From the management cluster (where ArgoCD is running)
./scripts/setup-external-cluster.ps1
```

Or with custom parameters:
```powershell
./scripts/setup-external-cluster.ps1 -ClusterId "0eae25c8-1244-4c33-89fb-5e03974780a6" -ClusterName "doks-target-cluster"
```

### 2. Verify Cluster Registration

Check that the cluster is properly registered:

```bash
# List clusters in ArgoCD
argocd cluster list

# Should show both clusters:
# - https://kubernetes.default.svc (in-cluster)
# - https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com (doks-target-cluster)
```

### 3. Deploy Applications

Applications will now automatically deploy to the correct cluster based on environment:

```bash
# Dev environment → Target cluster
./scripts/generate-manifests-cicd.ps1 -Environment "dev" -ProjectId "my-app" -AppName "My App"

# Staging environment → Target cluster  
./scripts/generate-manifests-cicd.ps1 -Environment "staging" -ProjectId "my-app" -AppName "My App"

# Production environment → Management cluster
./scripts/generate-manifests-cicd.ps1 -Environment "production" -ProjectId "my-app" -AppName "My App"
```

## Configuration Files

### Updated Templates

The ArgoCD application and project templates now use conditional cluster configuration:

```yaml
# templates/argocd/application.yaml
destination:
  server: {{#if CLUSTER_SERVER}}{{CLUSTER_SERVER}}{{else}}https://kubernetes.default.svc{{/if}}
  namespace: {{NAMESPACE}}
```

### Cluster Configuration Logic

The cluster configuration is determined in the generation scripts:

**Python Script** (`scripts/generate-manifests-cicd.py`):
```python
def get_cluster_config(environment):
    cluster_mappings = {
        "dev": {
            "server": "https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com",
            "name": "doks-target-cluster",
            "cluster_id": "0eae25c8-1244-4c33-89fb-5e03974780a6"
        },
        "staging": {
            "server": "https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com",
            "name": "doks-target-cluster", 
            "cluster_id": "0eae25c8-1244-4c33-89fb-5e03974780a6"
        },
        "production": {
            "server": "https://kubernetes.default.svc",
            "name": "in-cluster",
            "cluster_id": "ca0e9f31-fd81-43a8-bace-ef88bb156117"
        }
    }
    return cluster_mappings.get(environment, cluster_mappings["production"])
```

## Troubleshooting

### Common Issues

1. **ArgoCD namespace not found error**
   - Ensure you're running the setup from the management cluster
   - Verify ArgoCD is properly installed and accessible

2. **Cluster connection issues**
   - Check that doctl is authenticated: `doctl auth list`
   - Verify cluster access: `doctl kubernetes cluster kubeconfig save <cluster-id>`
   - Test connectivity: `kubectl cluster-info`

3. **Application sync failures**
   - Verify the target cluster is registered: `argocd cluster list`
   - Check ArgoCD application logs: `kubectl logs -n argocd deployment/argocd-application-controller`
   - Ensure proper RBAC permissions on target cluster

### Verification Commands

```bash
# Check ArgoCD cluster list
argocd cluster list

# Verify application deployment
argocd app list

# Check application status
argocd app get <app-name>

# View application logs
argocd app logs <app-name>
```

## Security Considerations

- The target cluster requires proper RBAC configuration for ArgoCD
- Service accounts and cluster roles are automatically created during cluster registration
- Network policies should allow communication between clusters if required
- Consider using private networking for cluster-to-cluster communication

## Next Steps

1. Run the setup script to register the target cluster
2. Test deployment with a dev environment application
3. Verify applications are created in the correct cluster
4. Monitor ArgoCD for any sync issues
5. Update CI/CD pipelines to use the new cluster configuration
