# Startup CI/CD Testing Guide

This guide provides comprehensive testing instructions to verify each environment's deployment flow works correctly in your GitHub Actions CI/CD pipeline.

## 🎯 Testing Overview

We'll test three distinct deployment flows:
1. **DEV**: Auto-deployment on main branch push
2. **STAGING**: Manual deployment with optional approval
3. **PRODUCTION**: Strict manual deployment with multi-stage approval

## 🧪 Pre-Testing Setup

### Prerequisites Checklist
- [ ] All three GitHub environments created (dev, staging, production)
- [ ] Repository secrets configured
- [ ] Branch protection rules enabled
- [ ] CODEOWNERS file updated with your team members
- [ ] Self-hosted runners available and connected

### Test Application Setup

Create a simple test application structure:

```bash
# Create test application directory
mkdir -p deployments/test-startup-app/overlays/{dev,staging,production}
mkdir -p deployments/test-startup-app/argocd
```

## 🔬 Test 1: DEV Environment Auto-Deployment

### Objective
Verify that pushing changes to main branch automatically triggers DEV deployment.

### Test Steps

1. **Create DEV Application Manifest**
   ```bash
   # Create deployments/test-startup-app/overlays/dev/application.yaml
   cat > deployments/test-startup-app/overlays/dev/application.yaml << 'EOF'
   apiVersion: argoproj.io/v1alpha1
   kind: Application
   metadata:
     name: test-startup-app-dev
     namespace: argocd
   spec:
     project: test-startup-app-project
     source:
       repoURL: https://github.com/your-org/your-repo
       targetRevision: HEAD
       path: deployments/test-startup-app/k8s
     destination:
       server: https://kubernetes.default.svc
       namespace: test-startup-app-dev
     syncPolicy:
       automated:
         prune: true
         selfHeal: true
   EOF
   ```

2. **Create ArgoCD Project**
   ```bash
   # Create deployments/test-startup-app/argocd/project.yaml
   cat > deployments/test-startup-app/argocd/project.yaml << 'EOF'
   apiVersion: argoproj.io/v1alpha1
   kind: AppProject
   metadata:
     name: test-startup-app-project
     namespace: argocd
   spec:
     description: Test startup application project
     sourceRepos:
     - 'https://github.com/your-org/your-repo'
     destinations:
     - namespace: 'test-startup-app-*'
       server: https://kubernetes.default.svc
     clusterResourceWhitelist:
     - group: ''
       kind: Namespace
     namespaceResourceWhitelist:
     - group: ''
       kind: '*'
     - group: 'apps'
       kind: '*'
   EOF
   ```

3. **Commit and Push to Main**
   ```bash
   git add deployments/test-startup-app/
   git commit -m "🧪 Add test application for DEV auto-deployment testing"
   git push origin main
   ```

4. **Verify Auto-Deployment**
   - Navigate to Actions tab in GitHub
   - Look for "🚀 Deploy to DEV Environment" workflow
   - Verify it starts automatically within 30 seconds
   - Check workflow logs for successful completion

### Expected Results
- ✅ Workflow triggers automatically on push
- ✅ DEV environment deployment completes successfully
- ✅ ArgoCD application created in cluster
- ✅ No approval gates required

### Troubleshooting
If the workflow doesn't trigger:
- Check `ENABLE_AUTO_DEPLOY` variable is set to `true`
- Verify changes are in `deployments/` directory
- Check workflow file syntax and permissions

## 🎯 Test 2: STAGING Environment Manual Deployment

### Objective
Verify manual deployment trigger works with optional approval workflow.

### Test Steps

1. **Create STAGING Application Manifest**
   ```bash
   # Create deployments/test-startup-app/overlays/staging/application.yaml
   cat > deployments/test-startup-app/overlays/staging/application.yaml << 'EOF'
   apiVersion: argoproj.io/v1alpha1
   kind: Application
   metadata:
     name: test-startup-app-staging
     namespace: argocd
   spec:
     project: test-startup-app-project
     source:
       repoURL: https://github.com/your-org/your-repo
       targetRevision: HEAD
       path: deployments/test-startup-app/k8s
     destination:
       server: https://kubernetes.default.svc
       namespace: test-startup-app-staging
     syncPolicy:
       automated:
         prune: true
         selfHeal: true
   EOF
   ```

2. **Commit STAGING Manifests**
   ```bash
   git add deployments/test-startup-app/overlays/staging/
   git commit -m "🎯 Add STAGING environment for test application"
   git push origin main
   ```

3. **Trigger Manual Deployment**
   - Navigate to Actions → "🎯 Deploy to STAGING Environment"
   - Click "Run workflow"
   - Fill in parameters:
     - **Project ID**: `test-startup-app`
     - **Docker image**: `nginx` (for testing)
     - **Docker tag**: `latest`
     - **Application type**: `web-app`
     - **Skip approval**: `false` (to test approval flow)
   - Click "Run workflow"

4. **Handle Approval Process**
   - If environment has required reviewers, approve the deployment
   - Monitor workflow progress through approval gates
   - Verify deployment completes after approval

### Expected Results
- ✅ Manual workflow trigger works
- ✅ Approval gate functions (if configured)
- ✅ STAGING deployment completes successfully
- ✅ ArgoCD application created with staging configuration

### Troubleshooting
If approval is stuck:
- Check environment reviewers are configured
- Verify reviewers have repository access
- Check if approval timeout is configured

## 🔒 Test 3: PRODUCTION Environment Strict Deployment

### Objective
Verify production deployment with multi-stage approval process and safety checks.

### Test Steps

1. **Create PRODUCTION Application Manifest**
   ```bash
   # Create deployments/test-startup-app/overlays/production/application.yaml
   cat > deployments/test-startup-app/overlays/production/application.yaml << 'EOF'
   apiVersion: argoproj.io/v1alpha1
   kind: Application
   metadata:
     name: test-startup-app-production
     namespace: argocd
   spec:
     project: test-startup-app-project
     source:
       repoURL: https://github.com/your-org/your-repo
       targetRevision: HEAD
       path: deployments/test-startup-app/k8s
     destination:
       server: https://kubernetes.default.svc
       namespace: test-startup-app-production
     syncPolicy:
       automated:
         prune: true
         selfHeal: true
   EOF
   ```

2. **Commit PRODUCTION Manifests**
   ```bash
   git add deployments/test-startup-app/overlays/production/
   git commit -m "🔒 Add PRODUCTION environment for test application"
   git push origin main
   ```

3. **Trigger Production Deployment**
   - Navigate to Actions → "🔒 Deploy to PRODUCTION Environment"
   - Click "Run workflow"
   - Fill in parameters:
     - **Project ID**: `test-startup-app`
     - **Docker image**: `nginx`
     - **Docker tag**: `stable`
     - **Application type**: `web-app`
     - **Staging verification**: `true`
     - **Emergency deployment**: `false`
   - Click "Run workflow"

4. **Navigate Through Approval Gates**
   - **Validation Gate**: Verify all checks pass
   - **Security Approval**: Monitor security review process
   - **Compliance Approval**: Check compliance validation
   - **CODEOWNER Approval**: Verify CODEOWNER simulation
   - **Final Deployment**: Monitor production deployment

### Expected Results
- ✅ All validation checks pass
- ✅ Security and compliance approval gates function
- ✅ CODEOWNER approval simulation works
- ✅ Production deployment includes safety delays
- ✅ Comprehensive logging and monitoring

### Troubleshooting
If validation fails:
- Check staging verification requirement
- Verify production manifests exist
- Check CODEOWNERS file configuration

## 🔄 Test 4: Emergency Production Deployment

### Objective
Test emergency deployment bypass mechanisms.

### Test Steps

1. **Trigger Emergency Deployment**
   - Navigate to Actions → "🔒 Deploy to PRODUCTION Environment"
   - Click "Run workflow"
   - Fill in parameters:
     - **Project ID**: `test-startup-app`
     - **Docker image**: `nginx`
     - **Docker tag**: `hotfix-1.0.1`
     - **Application type**: `web-app`
     - **Staging verification**: `false`
     - **Emergency deployment**: `true`
   - Click "Run workflow"

2. **Verify Emergency Process**
   - Check that staging verification is bypassed
   - Verify CODEOWNER approval is expedited
   - Monitor for emergency deployment warnings
   - Confirm safety delays are reduced

### Expected Results
- ✅ Emergency deployment bypasses some checks
- ✅ Appropriate warnings are displayed
- ✅ Deployment completes faster than normal
- ✅ Emergency deployment is clearly logged

## 📊 Test Results Documentation

### Test Summary Template

```markdown
## Test Execution Summary

**Date**: [Date]
**Tester**: [Your Name]
**Repository**: [Repository Name]
**Commit**: [Test Commit SHA]

### Test Results

| Test Case | Status | Duration | Notes |
|-----------|--------|----------|-------|
| DEV Auto-Deploy | ✅/❌ | [time] | [notes] |
| STAGING Manual | ✅/❌ | [time] | [notes] |
| PROD Strict | ✅/❌ | [time] | [notes] |
| Emergency Deploy | ✅/❌ | [time] | [notes] |

### Issues Found
- [List any issues discovered]

### Recommendations
- [List any improvements needed]
```

## 🔧 Advanced Testing Scenarios

### Scenario 1: Failed Deployment Recovery
1. Introduce an error in manifest (invalid YAML)
2. Trigger deployment
3. Verify error handling and rollback procedures

### Scenario 2: Concurrent Deployments
1. Trigger multiple environment deployments simultaneously
2. Verify proper queuing and resource management

### Scenario 3: Secret Rotation Testing
1. Update environment secrets
2. Trigger deployments to verify secret pickup

### Scenario 4: Branch Protection Testing
1. Attempt direct push to main (should fail)
2. Create PR and verify CODEOWNERS approval requirement

## 🚨 Post-Testing Cleanup

After testing, clean up test resources:

```bash
# Remove test application
rm -rf deployments/test-startup-app/

# Commit cleanup
git add -A
git commit -m "🧹 Clean up test application after CI/CD testing"
git push origin main

# Clean up ArgoCD applications (if needed)
kubectl delete application test-startup-app-dev -n argocd
kubectl delete application test-startup-app-staging -n argocd
kubectl delete application test-startup-app-production -n argocd
kubectl delete appproject test-startup-app-project -n argocd
```

## 📈 Success Criteria

Your CI/CD pipeline is successfully configured if:

- [ ] DEV environment auto-deploys on every main branch push
- [ ] STAGING environment requires manual trigger and optional approval
- [ ] PRODUCTION environment enforces strict approval process
- [ ] All approval gates function correctly
- [ ] Error handling and logging work as expected
- [ ] Emergency deployment procedures are available
- [ ] Security and compliance checks are enforced

## 🔗 Next Steps

After successful testing:

1. **Production Readiness**
   - Replace test applications with real applications
   - Configure production-grade secrets
   - Set up monitoring and alerting

2. **Team Training**
   - Train team members on deployment procedures
   - Document emergency response procedures
   - Create deployment runbooks

3. **Continuous Improvement**
   - Gather feedback from deployment experiences
   - Optimize approval processes
   - Add additional safety checks as needed
