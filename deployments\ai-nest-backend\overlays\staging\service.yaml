apiVersion: v1
kind: Service
metadata:
  name: ai-nest-backend-service
  labels:
    app: ai-nest-backend
    app.kubernetes.io/name: ai-nest-backend
    app.kubernetes.io/component: nest-backend
    app.kubernetes.io/part-of: ai-nest-backend
    app.kubernetes.io/version: "fb4ad9ad"
    app.kubernetes.io/managed-by: argocd
spec:
  type: LoadBalancer
  ports:
  - port: 3002
    targetPort: 3002
    protocol: TCP
    name: http
  selector:
    app: ai-nest-backend
