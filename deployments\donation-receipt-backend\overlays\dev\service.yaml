apiVersion: v1
kind: Service
metadata:
  name: donation-receipt-backend-service-dev
  labels:
    app: donation-receipt-backend
    app.kubernetes.io/name: donation-receipt-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/part-of: donation-receipt-backend
    app.kubernetes.io/version: "673d3c80"
    app.kubernetes.io/managed-by: argocd
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: donation-receipt-backend
