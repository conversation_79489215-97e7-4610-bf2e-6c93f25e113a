apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component

# Database init container component with dynamic placeholders
# This component provides environment-specific database init container configurations
# The main init container patch is now managed at the environment level
# This component only provides additional environment-specific patches

patches:
- target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
    labelSelector: "environment=dev"
  path: dev-patch.yaml
- target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
    labelSelector: "environment=staging"
  path: staging-patch.yaml
- target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
    labelSelector: "environment=production"
  path: production-patch.yaml
