# GitOps Code Ownership and Approval Requirements
# This file defines who must approve changes to specific parts of the GitOps repository
#
# Documentation: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# =============================================================================
# GLOBAL OWNERSHIP
# =============================================================================
# Global fallback - repository administrators
* @AshrafSyed25

# =============================================================================
# PRODUCTION ENVIRONMENT PROTECTION
# =============================================================================
# All production deployment files require approval from senior team members
# This includes manifests, configurations, and any production-related changes

# Production overlay directories for all projects
deployments/*/overlays/production/ @AshrafSyed25
deployments/*/overlays/production/* @AshrafSyed25

# Production-specific ArgoCD applications
deployments/*/overlays/production/application.yaml @AshrafSyed25

# Production secrets and configurations
deployments/*/overlays/production/kustomization.yaml @AshrafSyed25
deployments/*/overlays/production/configmap.yaml @AshrafSyed25
deployments/*/overlays/production/secret.yaml @AshrafSyed25

# =============================================================================
# STAGING ENVIRONMENT PROTECTION
# =============================================================================
# Staging environment requires approval for quality assurance
# This ensures staging deployments are reviewed before production promotion

# Staging overlay directories for all projects
deployments/*/overlays/staging/ @AshrafSyed25
deployments/*/overlays/staging/* @AshrafSyed25

# Staging-specific ArgoCD applications
deployments/*/overlays/staging/application.yaml @AshrafSyed25

# =============================================================================
# ARGOCD PROJECT PROTECTION
# =============================================================================
# ArgoCD project definitions require approval as they control access and permissions

# ArgoCD project files for all applications
deployments/*/argocd/ @AshrafSyed25
deployments/*/argocd/project.yaml @AshrafSyed25


# =============================================================================
# REPOSITORY GOVERNANCE
# =============================================================================
# Repository configuration and governance files

# This CODEOWNERS file itself
.github/CODEOWNERS @AshrafSyed25


