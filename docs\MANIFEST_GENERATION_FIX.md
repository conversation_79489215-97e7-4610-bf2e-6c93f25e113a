# GitOps Manifest Generation Fix

## Issue Summary

The GitOps deployment workflow was failing with the error:
```
❌ Project manifest directory not found: deployments/at-spring-backend-sample
```

## Root Cause

The workflow had a **chicken-and-egg problem**:

1. **Line 276**: The workflow was setting `MANIFEST_DIR="deployments/$PROJECT_ID"`
2. **Lines 278-284**: It was checking if that directory exists and failing if it doesn't
3. **Lines 288-293**: Only then would it run the deployment script

However, the deployment script is **supposed to create** the `deployments/{project_id}` directory, not use it as input.

## The Fix

### ✅ Fixed Workflow Logic

**Before (Incorrect)**:
```bash
# Extract project_id from payload to determine manifest directory
PROJECT_ID=$(echo "$PAYLOAD" | jq -r '.project_id')
MANIFEST_DIR="deployments/$PROJECT_ID"

# Check if project-specific manifest directory exists
if [ ! -d "$MANIFEST_DIR" ]; then
  echo "❌ Project manifest directory not found: $MANIFEST_DIR"
  exit 1
fi

python3 scripts/deploy.py \
  --manifest-dir "$MANIFEST_DIR" \
  # ... other args
```

**After (Correct)**:
```bash
# Extract project_id from payload
PROJECT_ID=$(echo "$PAYLOAD" | jq -r '.project_id')

# Use manifests directory as template source
TEMPLATE_DIR="manifests"

# Check if template directory exists
if [ ! -d "$TEMPLATE_DIR" ]; then
  echo "❌ Template directory not found: $TEMPLATE_DIR"
  exit 1
fi

python3 scripts/deploy.py \
  --manifest-dir "$TEMPLATE_DIR" \
  # ... other args
```

### ✅ Correct Flow

1. **Template Source**: `manifests/` directory (contains templates with placeholders)
2. **Processing**: `process_payload.py` copies templates and replaces placeholders
3. **Final Output**: `deploy.py` creates `deployments/{project_id}/` with processed manifests

## Verification

The fix has been tested and verified:

```bash
python test-manifest-generation.py
```

**Test Results**:
- ✅ Template directory structure validated
- ✅ Manifest generation process works correctly
- ✅ Project directory created under `deployments/{project_id}/`
- ✅ All required subdirectories created (base, overlays, argocd)

## Project ID Discrepancy

The error showed `at-spring-backend-sample` but the codebase references `ai-spring-backend-sample`. This suggests:

1. **Typo in Payload**: Someone sent a dispatch event with `project_id: "at-spring-backend-sample"`
2. **Should be**: `project_id: "ai-spring-backend-sample"`

## Prevention

### ✅ Payload Validation

The workflow already logs the payload for debugging:
```yaml
echo "Client payload: ${{ toJson(github.event.client_payload) }}"
```

### ✅ Template Structure

Ensure the `manifests/` directory always contains:
```
manifests/
├── base/
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── configmap.yaml
│   ├── secret.yaml
│   └── kustomization.yaml
├── overlays/
│   ├── dev/
│   ├── staging/
│   └── production/
├── argocd/
│   └── project.yaml
└── components/
    ├── common-labels/
    ├── database-init/
    └── resource-limits/
```

### ✅ Correct Payload Format

When triggering deployments, ensure the payload uses the correct `project_id`:

```json
{
  "app_name": "AI Spring Backend Sample",
  "project_id": "ai-spring-backend-sample",
  "application_type": "springboot-backend",
  "environment": "dev",
  "docker_image": "registry.digitalocean.com/doks-registry/ai-spring-backend",
  "docker_tag": "latest",
  "source_repo": "ChidhagniConsulting/ai-spring-backend",
  "source_branch": "main",
  "commit_sha": "abc123def456"
}
```

## Next Steps

1. **✅ Fixed**: Workflow logic corrected
2. **✅ Tested**: Manifest generation verified
3. **🔄 Deploy**: Next deployment should work correctly
4. **📋 Monitor**: Check workflow logs for successful execution

## Files Modified

- `.github/workflows/deploy-from-cicd.yaml` - Fixed manifest directory logic
- `test-manifest-generation.py` - Added verification test

The deployment workflow should now work correctly for any project with a valid payload and the `manifests/` template directory structure.
