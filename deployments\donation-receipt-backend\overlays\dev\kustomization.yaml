apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: donation-receipt-backend-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- ingress.yaml
# Note: secret.yaml is conditionally included based on APPLICATION_TYPE
# For react-frontend applications, secrets are handled differently

components:
- ../../components/common-labels
# Note: react-frontend component is conditionally included based on APPLICATION_TYPE

labels:
- pairs:
    app: donation-receipt-backend
    app.kubernetes.io/name: donation-receipt-backend
    app.kubernetes.io/part-of: donation-receipt-backend
    app.kubernetes.io/component: springboot-backend
    app.kubernetes.io/version: "673d3c80"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting-Donation-Receipt-Backend
    source.branch: 78-merge

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: donation-receipt-backend
# Note: init-container-patch.yaml is conditionally included based on APPLICATION_TYPE
# For backend applications (springboot, django, nest), database init containers are applied

# Image transformations are applied after patches
# This allows dynamic tag updates without modifying the deployment directly

namePrefix: ""
nameSuffix: "-dev"
