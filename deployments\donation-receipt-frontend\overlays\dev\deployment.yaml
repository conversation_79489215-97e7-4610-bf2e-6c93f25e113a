apiVersion: apps/v1
kind: Deployment
metadata:
  name: donation-receipt-frontend
  labels:
    app: donation-receipt-frontend
    app.kubernetes.io/name: donation-receipt-frontend
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/part-of: donation-receipt-frontend
    app.kubernetes.io/version: "0b08fe33"
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: donation-receipt-frontend
  template:
    metadata:
      labels:
        app: donation-receipt-frontend
        app.kubernetes.io/name: donation-receipt-frontend
        app.kubernetes.io/component: react-frontend
        app.kubernetes.io/part-of: donation-receipt-frontend
        app.kubernetes.io/version: "0b08fe33"
    spec:
      containers:
      - name: donation-receipt-frontend
        image: registry.digitalocean.com/chidhagni-doks-registry/donation-receipt-frontend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
        envFrom:
        - configMapRef:
            name: donation-receipt-frontend-config
       
        
        
        
        
        
        
        
        
        
        # React Frontend Health Checks (TCP for static content)
        livenessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
