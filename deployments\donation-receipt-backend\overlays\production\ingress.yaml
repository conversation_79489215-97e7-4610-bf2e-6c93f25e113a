apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: PLACEHOLDER_PROJECT_ID-ingress
  labels:
    app: PLACEHOLDER_PROJECT_ID
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - PLACEHOLDER_HOST
    secretName: PLACEHOLDER_PROJECT_ID-tls
  rules:
  - host: PLACEHOLDER_HOST
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: PLACEHOLDER_APP_NAME-service-production
            port:
              number: PLACEHOLDER_CONTAINER_PORT
