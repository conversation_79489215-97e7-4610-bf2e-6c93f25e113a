apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-django-backend
  labels:
    app: ai-django-backend
    app.kubernetes.io/name: ai-django-backend
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/part-of: ai-django-backend
    app.kubernetes.io/version: "f4509a52"
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai-django-backend
  template:
    metadata:
      labels:
        app: ai-django-backend
        app.kubernetes.io/name: ai-django-backend
        app.kubernetes.io/component: django-backend
        app.kubernetes.io/part-of: ai-django-backend
        app.kubernetes.io/version: "f4509a52"
    spec:
      containers:
      - name: ai-django-backend
        image: registry.digitalocean.com/doks-registry/ai-django-backend:latest-staging
        imagePullPolicy: Always
        ports:
        - containerPort: 8001
          name: http
        envFrom:
        - configMapRef:
            name: ai-django-backend-config
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: JWT_SECRET
        - name: SESSION_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: SESSION_SECRET
        - name: SESSION_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: SESSION_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_PASSWORD
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_PORT
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_NAME
        - name: DB_SSL_MODE
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DB_SSL_MODE
        
        
        # Django specific database configuration
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: DATABASE_URL
        - name: DJANGO_SETTINGS_MODULE
          valueFrom:
            configMapKeyRef:
              name: ai-django-backend-config
              key: DJANGO_SETTINGS_MODULE
        
        
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-django-backend-secrets
              key: GOOGLE_CLIENT_SECRET
        
        
        # Django Health Checks
        livenessProbe:
          httpGet:
            path: /health/
            port: 8001
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/
            port: 8001
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health/
            port: 8001
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 9
        
        
        
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
