# Multi-Application Type Staging Deployment

This document explains how to use the enhanced staging deployment workflow that supports multiple application types with conditional deployment logic.

## 🎯 Overview

The `deploy-staging.yml` workflow now supports three different application types:

1. **Spring Boot Applications** (`springboot-backend`) - Java/Spring framework applications
2. **NestJS Applications** (`nest-backend`) - Node.js/TypeScript applications using NestJS
3. **Django Applications** (`django-backend`) - Python applications using Django framework

Each application type has its own deployment flow with specific secrets and configuration.

## 🚀 Quick Start

### 1. Configure Secrets

Before deploying, ensure you have configured the required secrets for your application type:

```bash
# Run the validation script to check your secrets
./scripts/validate-app-secrets.sh
```

### 2. Trigger Deployment

1. Go to your repository on GitHub
2. Navigate to **Actions** → **🎯 Deploy to STAGING Environment**
3. Click **Run workflow**
4. Fill in the required parameters:
   - **Project ID**: Your application identifier (e.g., `my-spring-app`)
   - **Docker Image**: Image name without tag (e.g., `registry.digitalocean.com/my-registry/app`)
   - **Docker Tag**: Tag to deploy (e.g., `v1.0.0`)
   - **Container Port**: Application port (8080 for Spring, 3000 for NestJS, 8000 for Django)
   - **Application Type**: Select your application type from the dropdown
   - **Skip Approval**: Optional, for testing purposes

## 📋 Application Type Details

### Spring Boot Applications

**Application Type**: `springboot-backend`  
**Default Port**: `8080`  
**Framework**: Java Spring Boot

**Required Secrets**:
- `JWT_SECRET_SPRING`
- `DB_HOST_SPRING_STAGING`
- `DB_USER_SPRING_STAGING`
- `DB_PASSWORD_SPRING_STAGING`
- `DB_NAME_SPRING_STAGING`

**Example Deployment**:
```yaml
project_id: "my-spring-app"
docker_image: "registry.digitalocean.com/my-registry/spring-app"
docker_tag: "v1.0.0"
host_name: "devapi.pheart.in"
application_type: "springboot-backend"
```

### NestJS Applications

**Application Type**: `nest-backend`  
**Default Port**: `3000`  
**Framework**: Node.js NestJS

**Required Secrets**:
- `JWT_SECRET_NEST`
- `SESSION_SECRET_NEST`
- `DB_HOST_NEST_STAGING`
- `DB_USER_NEST_STAGING`
- `DB_PASSWORD_NEST_STAGING`
- `DB_NAME_NEST_STAGING`

**Example Deployment**:
```yaml
project_id: "my-nest-app"
docker_image: "registry.digitalocean.com/my-registry/nest-app"
docker_tag: "v1.0.0"
host_name: "devapi.pheart.in"
application_type: "nest-backend"
```

### Django Applications

**Application Type**: `django-backend`  
**Default Port**: `8000`  
**Framework**: Python Django

**Required Secrets**:
- `JWT_SECRET_DJANGO`
- `DJANGO_SECRET_KEY`
- `SESSION_SECRET_DJANGO`
- `DB_HOST_DJANGO_STAGING`
- `DB_USER_DJANGO_STAGING`
- `DB_PASSWORD_DJANGO_STAGING`
- `DB_NAME_DJANGO_STAGING`

**Example Deployment**:
```yaml
project_id: "my-django-app"
docker_image: "registry.digitalocean.com/my-registry/django-app"
docker_tag: "v1.0.0"
host_name: "devapi.pheart.in"
application_type: "django-backend"
```

## 🔄 Workflow Process

The enhanced workflow follows these steps:

1. **Validation**: Validates input parameters and application type
2. **Approval Gate**: Optional approval process (can be skipped for testing)
3. **Secret Preparation**: Loads application-specific secrets based on type
4. **Docker Image Promotion**: Tags and promotes image for staging
5. **Manifest Generation**: Creates Kubernetes manifests with correct configuration
6. **ArgoCD Deployment**: Deploys to staging environment via ArgoCD

## 🔐 Security Features

### Application-Specific Secrets
- Each application type uses its own set of secrets
- No cross-contamination between application types
- Environment-specific database configurations

### Validation
- Automatic validation of required secrets for each application type
- Clear error messages for missing configuration
- Pre-deployment checks to prevent failures

### Isolation
- Each application gets its own namespace in Kubernetes
- Separate database instances for different application types
- Independent scaling and resource management

## 🛠️ Configuration Management

### Environment Variables
The workflow automatically configures environment-specific variables:

- **Database URLs**: Generated based on application type and secrets
- **Authentication**: JWT and session secrets specific to each framework
- **Service Configuration**: Framework-specific settings (Django secret key, NestJS session secret, etc.)

### Kubernetes Resources
Generated manifests include:

- **Deployment**: Application-specific container configuration
- **Service**: Correct port mapping for each application type
- **Secrets**: Base64-encoded secrets specific to the application
- **ConfigMap**: Framework-specific configuration
- **Ingress**: Routing configuration (if applicable)

## 🔍 Monitoring and Debugging

### Workflow Logs
Each step provides detailed logging:

```bash
🔍 Validating Spring Boot application secrets...
📦 Configuring Spring Boot secrets...
✅ Staging promotion payload prepared for springboot-backend application.
```

### ArgoCD Integration
- Applications are automatically registered in ArgoCD
- Health checks specific to each application type
- Rollback capabilities maintained

### Troubleshooting
Common issues and solutions:

1. **Missing Secrets**: Use the validation script to identify missing secrets
2. **Wrong Port**: Ensure container port matches application type defaults
3. **Database Connection**: Verify staging database secrets are correct
4. **Image Not Found**: Check Docker image name and tag exist in registry

## 📚 Additional Resources

- [Multi-Application Deployment Secrets Configuration](./multi-app-deployment-secrets.md)
- [Environment-Specific Secrets Configuration](./environment-secrets-configuration.md)
- [Validation Script Usage](../scripts/validate-app-secrets.sh)

## 🔄 Migration Guide

### From Single Application Type
If you're migrating from the previous Spring-only workflow:

1. **Keep existing secrets** - Spring Boot deployments will continue to work
2. **Add new application secrets** - Only for new application types you plan to deploy
3. **Update deployment parameters** - Ensure `application_type` is correctly specified
4. **Test with staging** - Verify new configuration before production use

### Best Practices
- Use separate database instances for each application type in staging
- Test each application type deployment independently
- Monitor resource usage and adjust limits as needed
- Keep secrets organized with clear naming conventions
