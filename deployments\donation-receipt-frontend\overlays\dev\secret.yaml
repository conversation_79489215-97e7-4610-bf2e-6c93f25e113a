apiVersion: v1
kind: Secret
metadata:
  name: donation-receipt-frontend-secrets
  labels:
    app: donation-receipt-frontend
    app.kubernetes.io/name: donation-receipt-frontend
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: donation-receipt-frontend
    app.kubernetes.io/version: "0b08fe33"
    app.kubernetes.io/managed-by: argocd
    environment: dev
type: Opaque
data:
  # Development Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  

  

  

  

  

  

  # Development-specific secrets
  # DEBUG_MODE: DYNAMIC_DEBUG_MODE_B64
  # LOG_LEVEL: DYNAMIC_LOG_LEVEL_B64
