apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: pure-heart-frontend-guest-dev
  namespace: argocd
  labels:
    app.kubernetes.io/name: pure-heart-frontend-guest-dev
    app.kubernetes.io/part-of: pure-heart-frontend-guest
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/version: "4a061f0f"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    app-type: react-frontend
    source.repo: ChidhagniConsulting-Pure-Heart-Frontend-Guest
    source.branch: 9-merge
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: pure-heart-frontend-guest-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/pure-heart-frontend-guest/overlays/dev
  destination:
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
    namespace: pure-heart-frontend-guest-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Development environment for pure-heart-frontend-guest"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/Pure-Heart-Frontend-Guest"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "react-frontend"
  - name: Source Branch
    value: "9/merge"
  - name: Commit SHA
    value: "4a061f0f"
  - name: Configuration
    value: "Development configuration with debug enabled"
