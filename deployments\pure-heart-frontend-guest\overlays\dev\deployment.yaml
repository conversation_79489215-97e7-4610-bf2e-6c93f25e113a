apiVersion: apps/v1
kind: Deployment
metadata:
  name: pure-heart-frontend-guest
  labels:
    app: pure-heart-frontend-guest
    app.kubernetes.io/name: pure-heart-frontend-guest
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/part-of: pure-heart-frontend-guest
    app.kubernetes.io/version: "4a061f0f"
    app.kubernetes.io/managed-by: argocd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pure-heart-frontend-guest
  template:
    metadata:
      labels:
        app: pure-heart-frontend-guest
        app.kubernetes.io/name: pure-heart-frontend-guest
        app.kubernetes.io/component: react-frontend
        app.kubernetes.io/part-of: pure-heart-frontend-guest
        app.kubernetes.io/version: "4a061f0f"
    spec:
      containers:
      - name: pure-heart-frontend-guest
        image: registry.digitalocean.com/chidhagni-doks-registry/pure-heart-frontend-guest:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3003
          name: http
        envFrom:
        - configMapRef:
            name: pure-heart-frontend-guest-config
       
        
        
        
        
        
        
        
        
        
        # React Frontend Health Checks (TCP for static content)
        livenessProbe:
          tcpSocket:
            port: 3003
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: 3003
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
