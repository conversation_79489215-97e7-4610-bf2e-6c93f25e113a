apiVersion: v1
kind: Service
metadata:
  name: pure-heart-frontend-guest-service-dev
  labels:
    app: pure-heart-frontend-guest
    app.kubernetes.io/name: pure-heart-frontend-guest
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/part-of: pure-heart-frontend-guest
    app.kubernetes.io/version: "4a061f0f"
    app.kubernetes.io/managed-by: argocd
spec:
  type: ClusterIP
  ports:
  - port: 3003
    targetPort: 3003
    protocol: TCP
    name: http
  selector:
    app: pure-heart-frontend-guest
