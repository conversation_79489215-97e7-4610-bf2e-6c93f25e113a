apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-django-backend-staging
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-django-backend-staging
    app.kubernetes.io/part-of: ai-django-backend
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/version: "f4509a52"
    app.kubernetes.io/managed-by: argocd
    environment: staging
    app-type: django-backend
    source.repo: ChidhagniConsulting-gitops-argocd-apps
    source.branch: main
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-django-backend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: deployments/ai-django-backend/overlays/staging
  destination:
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
    namespace: ai-django-backend-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "Staging environment for ai-django-backend"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "staging"
  - name: Application Type
    value: "django-backend"
  - name: Source Branch
    value: "main"
  - name: Commit SHA
    value: "f4509a52"
  - name: Configuration
    value: "Staging configuration with production-like settings"
