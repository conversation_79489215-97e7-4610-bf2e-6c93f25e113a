# Automated Health Check Path Example

This example demonstrates how the GitOps system automatically sets health check paths based on application type, eliminating the need to include `health_check_path` in the payload.

## The Problem Solved

Previously, you had to include `health_check_path` in every payload, which:
- Increased payload size (contributing to GitHub's 10+ variable limit)
- Required manual specification of type-specific paths
- Could lead to incorrect health check configurations

## The Solution

The system now automatically sets the correct health check path based on `application_type`:

| Application Type | Automatic Health Check Path |
|------------------|----------------------------|
| `react-frontend` | `/` |
| `springboot-backend` | `/actuator/health` |
| Other types | `/health` |

## Example Payloads

### React Frontend - Full Payload (9 fields)

```json
{
  "app_name": "My React Dashboard",
  "project_id": "react-dashboard",
  "application_type": "react-frontend",
  "environment": "production",
  "docker_image": "myorg/react-dashboard",
  "docker_tag": "v2.1.0",
  "host_name": "devapi.pheart.in",
  "source_repo": "myorg/react-dashboard",
  "source_branch": "main",
  "commit_sha": "abc123def456"
}
```

**Result**: Health check path automatically set to `/` (even if you tried to override it)

### Spring Boot Backend - Full Payload (9 fields)

```json
{
  "app_name": "User Auth API",
  "project_id": "auth-api",
  "application_type": "springboot-backend",
  "environment": "production",
  "docker_image": "myorg/auth-api",
  "docker_tag": "v1.5.2",
  "host_name": "devapi.pheart.in",
  "source_repo": "myorg/auth-api",
  "source_branch": "main",
  "commit_sha": "def456ghi789"
}
```

**Result**: Health check path automatically set to `/actuator/health` (even if you tried to override it)

## Testing the Automation

### Test 1: React Frontend with Override Attempt

```bash
# Try to override health check path for React app
python scripts/generate-manifests-cicd.py \
  --app-name "Test React" \
  --project-id "test-react" \
  --application-type "react-frontend" \
  --environment "dev" \
  --docker-image "nginx" \
  --docker-tag "alpine" \
  --health-check-path "/custom-health"  # This will be ignored
```

**Output**: `Health: /` (automatically set, override ignored)

### Test 2: Spring Boot with Override Attempt

```bash
# Try to override health check path for Spring Boot app
python scripts/generate-manifests-cicd.py \
  --app-name "Test Spring" \
  --project-id "test-spring" \
  --application-type "springboot-backend" \
  --environment "dev" \
  --docker-image "openjdk" \
  --docker-tag "11-jre-slim" \
  --health-check-path "/custom-health"  # This will be ignored
```

**Output**: `Health: /actuator/health` (automatically set, override ignored)

## Generated Deployment Manifests

### React Frontend Health Checks

```yaml
# Automatically generated for react-frontend
livenessProbe:
  httpGet:
    path: /  # Always / for React frontends
    port: 3000
  initialDelaySeconds: 30
  periodSeconds: 30
readinessProbe:
  httpGet:
    path: /  # Always / for React frontends
    port: 3000
  initialDelaySeconds: 10
  periodSeconds: 10
```

### Spring Boot Backend Health Checks

```yaml
# Automatically generated for springboot-backend
livenessProbe:
  httpGet:
    path: /actuator/health  # Always /actuator/health for Spring Boot
    port: 9090
  initialDelaySeconds: 90
  periodSeconds: 30
readinessProbe:
  httpGet:
    path: /actuator/health  # Always /actuator/health for Spring Boot
    port: 9090
  initialDelaySeconds: 60
  periodSeconds: 10
```

## CI/CD Integration

### React Frontend Deployment

```yaml
name: Deploy React App
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy via GitOps
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "React Dashboard",
              "project_id": "react-dashboard",
              "application_type": "react-frontend",
              "environment": "production",
              "docker_image": "myorg/react-dashboard",
              "docker_tag": "${{ github.sha }}",
              "host_name": "devapi.pheart.in",
              "source_repo": "${{ github.repository }}",
              "source_branch": "${{ github.ref_name }}",
              "commit_sha": "${{ github.sha }}"
            }
```

**Note**: 
- `health_check_path` (/) and `container_port` (3000) are automatically determined from application type and added to secrets
- No need to specify these values in the payload - they're handled securely

### Spring Boot Backend Deployment

```yaml
name: Deploy Spring Boot API
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy via GitOps
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          repository: ChidhagniConsulting/gitops-argocd-apps
          event-type: deploy-to-argocd
          client-payload: |
            {
              "app_name": "Auth API",
              "project_id": "auth-api",
              "application_type": "springboot-backend",
              "environment": "production",
              "docker_image": "myorg/auth-api",
              "docker_tag": "${{ github.sha }}",
              "host_name": "devapi.pheart.in",
              "source_repo": "${{ github.repository }}",
              "source_branch": "${{ github.ref_name }}",
              "commit_sha": "${{ github.sha }}"
            }
```

**Note**: 
- `health_check_path` (/actuator/health) and `container_port` (8080) are automatically determined from application type and added to secrets
- No need to specify these values in the payload - they're handled securely

## Benefits

1. **✅ Reduced Payload Size**: One less field to worry about
2. **✅ Correct Defaults**: Always gets the right health check path for the application type
3. **✅ No Override Confusion**: Can't accidentally set wrong health check path
4. **✅ Simplified CI/CD**: Less configuration required in deployment pipelines
5. **✅ GitHub Limits**: Helps stay under the 10+ variable payload limitation

## Validation

You can verify the automation is working by checking the generated deployment files:

```bash
# Check React Frontend health check
grep -A 5 -B 5 "livenessProbe" test-automated/test-react-full/k8s/deployment.yaml

# Check Spring Boot health check  
grep -A 5 -B 5 "livenessProbe" test-automated/test-spring-full/k8s/deployment.yaml
```

The health check paths will always be correct for the application type, regardless of what was specified in the payload.

## Cleanup

```bash
# Remove test files
rm -rf test-automated/
```
