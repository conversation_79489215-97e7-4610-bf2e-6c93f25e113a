apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: donation-receipt-backend-ingress
  labels:
    app: donation-receipt-backend
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - devapi.pheart.in
    secretName: donation-receipt-backend-tls
  rules:
  - host: devapi.pheart.in
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: donation-receipt-backend-service-dev
            port:
              number: 8080
