apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: PLACEHOLDER_PROJECT_ID-staging

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- ingress.yaml
# Note: secret.yaml is conditionally included based on APPLICATION_TYPE
# For react-frontend applications, secrets are handled differently

components:
- ../../components/common-labels
# Note: react-frontend component is conditionally included based on APPLICATION_TYPE

labels:
- pairs:
    app: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/name: PLACEHOLDER_PROJECT_ID
    app.kubernetes.io/part-of: PLACEHOLDER_APP_NAME
    app.kubernetes.io/component: PLACEHOLDER_APPLICATION_TYPE
    app.kubernetes.io/version: PLACEHOLDER_COMMIT_SHA
    app.kubernetes.io/managed-by: argocd
    environment: staging
    source.repo: PLACEHOLDER_SOURCE_REPO_LABEL
    source.branch: PLACEHOLDER_SOURCE_BRANCH_LABEL

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: PLACEHOLDER_PROJECT_ID
# Note: init-container-patch.yaml is conditionally included based on APPLICATION_TYPE
# For backend applications (springboot, django, nest), database init containers are applied

namePrefix: ""
nameSuffix: "-staging"
