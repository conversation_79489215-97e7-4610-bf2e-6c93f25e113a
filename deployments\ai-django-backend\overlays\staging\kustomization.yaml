apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-django-backend-staging

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml

components:
- ../../components/common-labels


labels:
- pairs:
    app: ai-django-backend
    app.kubernetes.io/name: ai-django-backend
    app.kubernetes.io/part-of: ai-django-backend
    app.kubernetes.io/component: django-backend
    app.kubernetes.io/version: "f4509a52"
    app.kubernetes.io/managed-by: argocd
    environment: staging
    source.repo: ChidhagniConsulting-gitops-argocd-apps
    source.branch: main

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: ai-django-backend


- path: init-container-patch.yaml
  target:
    kind: Deployment
    name: ai-django-backend



namePrefix: ""
nameSuffix: "-staging"
