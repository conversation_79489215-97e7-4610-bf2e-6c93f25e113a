# Cross-Cluster ArgoCD Troubleshooting Guide

This guide helps resolve common issues when setting up cross-cluster ArgoCD deployment.

## Common Issues and Solutions

### 1. "ArgoCD namespace not found" Error

**Problem**: Applications fail to deploy with namespace errors on the target cluster.

**Root Cause**: ArgoCD tries to create resources in the `argocd` namespace on the target cluster, but ArgoCD should only exist on the management cluster.

**Solution**:
```bash
# Verify cluster registration
argocd cluster list

# Check application destination
argocd app get <app-name> -o yaml | grep -A 5 destination

# Ensure applications target correct cluster
# Dev/Staging → Target cluster (0eae25c8-1244-4c33-89fb-5e03974780a6)
# Production → Management cluster (https://kubernetes.default.svc)
```

### 2. Cluster Registration Failures

**Problem**: `argocd cluster add` command fails.

**Possible Causes & Solutions**:

#### A. Kubeconfig Context Issues
```bash
# List available contexts
kubectl config get-contexts

# Ensure target cluster context exists
doctl kubernetes cluster kubeconfig save 0eae25c8-1244-4c33-89fb-5e03974780a6

# Verify context name
kubectl config get-contexts | grep 0eae25c8-1244-4c33-89fb-5e03974780a6
```

#### B. RBAC Permissions
```bash
# Check if ArgoCD can access target cluster
kubectl auth can-i '*' '*' --context=do-0eae25c8-1244-4c33-89fb-5e03974780a6

# Verify service account creation
kubectl get serviceaccounts -n kube-system --context=do-0eae25c8-1244-4c33-89fb-5e03974780a6 | grep argocd
```

#### C. Network Connectivity
```bash
# Test cluster connectivity
kubectl cluster-info --context=do-0eae25c8-1244-4c33-89fb-5e03974780a6

# Check if management cluster can reach target cluster
kubectl get nodes --context=do-0eae25c8-1244-4c33-89fb-5e03974780a6
```

### 3. Application Sync Issues

**Problem**: Applications show "OutOfSync" or "Unknown" status.

**Diagnosis**:
```bash
# Check application status
argocd app get <app-name>

# View application events
argocd app get <app-name> -o yaml | grep -A 10 status

# Check application logs
argocd app logs <app-name>
```

**Common Solutions**:

#### A. Manifest Path Issues
```yaml
# Verify source path in application.yaml
spec:
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: <project-id>/k8s  # Ensure this path exists
```

#### B. Namespace Creation
```yaml
# Ensure CreateNamespace is enabled
spec:
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
```

#### C. Resource Permissions
```yaml
# Check AppProject permissions
spec:
  destinations:
  - namespace: <target-namespace>
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
```

### 4. Authentication Issues

**Problem**: ArgoCD CLI authentication failures.

**Solutions**:
```bash
# Re-login to ArgoCD
argocd login <argocd-server>

# Or use port-forward if direct access isn't available
kubectl port-forward svc/argocd-server -n argocd 8080:443
argocd login localhost:8080

# Verify login
argocd account get-user-info
```

### 5. Cluster Status Issues

**Problem**: Cluster shows as "Unknown" or "Disconnected" in ArgoCD.

**Diagnosis**:
```bash
# Check cluster status
argocd cluster get https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com

# Verify cluster connectivity
kubectl get nodes --context=do-0eae25c8-1244-4c33-89fb-5e03974780a6
```

**Solutions**:
```bash
# Remove and re-add cluster
argocd cluster rm https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
argocd cluster add do-0eae25c8-1244-4c33-89fb-5e03974780a6 --name doks-target-cluster

# Update cluster credentials
doctl kubernetes cluster kubeconfig save 0eae25c8-1244-4c33-89fb-5e03974780a6
```

## Verification Commands

### Check Overall Setup
```bash
# 1. Verify ArgoCD is running on management cluster
kubectl get pods -n argocd

# 2. List registered clusters
argocd cluster list

# 3. Check cluster connectivity
argocd cluster get https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com

# 4. List applications
argocd app list

# 5. Check specific application
argocd app get <app-name>
```

### Validate Environment Mapping
```bash
# Dev environment should target external cluster
argocd app get <dev-app> -o yaml | grep server
# Expected: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com

# Production environment should target management cluster
argocd app get <prod-app> -o yaml | grep server
# Expected: https://kubernetes.default.svc
```

## Best Practices

### 1. Environment Separation
- **Dev/Staging**: Deploy to target cluster (0eae25c8-1244-4c33-89fb-5e03974780a6)
- **Production**: Deploy to management cluster (ca0e9f31-fd81-43a8-bace-ef88bb156117)

### 2. Namespace Strategy
- Use environment-specific namespaces: `<app-name>-<environment>`
- Avoid creating `argocd` namespace on target cluster

### 3. RBAC Configuration
- Ensure ArgoCD service account has necessary permissions on target cluster
- Use least-privilege principle for cross-cluster access

### 4. Monitoring
- Monitor cluster connectivity regularly
- Set up alerts for application sync failures
- Use ArgoCD notifications for deployment status

## Emergency Recovery

### If Target Cluster Becomes Unreachable
```bash
# 1. Check cluster status
doctl kubernetes cluster get 0eae25c8-1244-4c33-89fb-5e03974780a6

# 2. Update kubeconfig
doctl kubernetes cluster kubeconfig save 0eae25c8-1244-4c33-89fb-5e03974780a6

# 3. Re-register with ArgoCD
argocd cluster add do-0eae25c8-1244-4c33-89fb-5e03974780a6 --name doks-target-cluster
```

### If Applications Fail to Sync
```bash
# 1. Force refresh
argocd app get <app-name> --refresh

# 2. Hard refresh
argocd app get <app-name> --hard-refresh

# 3. Manual sync
argocd app sync <app-name>

# 4. Sync with force
argocd app sync <app-name> --force
```

## Support Information

For additional help:
1. Check ArgoCD logs: `kubectl logs -n argocd deployment/argocd-application-controller`
2. Review application controller logs for specific errors
3. Use ArgoCD UI for visual debugging
4. Check GitHub Actions logs for CI/CD pipeline issues
