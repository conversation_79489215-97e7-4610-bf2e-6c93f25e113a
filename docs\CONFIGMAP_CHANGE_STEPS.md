# What You Need to Do After Changing URLs in ConfigMap

## 🚀 Essential Steps (Required Every Time)

### 1. Apply ConfigMap Changes
```bash
kubectl replace -f ai-react-frontend/k8s/configmap.yaml
```

### 2. Restart Deployment
```bash
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
```

### 3. Wait for Completion
```bash
kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev
```

## ✅ Verify Changes

### Check External Endpoint
```bash
# Test the configuration endpoint
curl http://152.42.156.235:3000/env-config.js

# Or using PowerShell
Invoke-WebRequest -Uri "http://152.42.156.235:3000/env-config.js" -UseBasicParsing
```

### Check Pod Configuration
```bash
kubectl exec -n ai-react-frontend-dev deployment/ai-react-frontend -- cat /usr/share/nginx/html/env-config.js
```

## 📝 Commit Changes (GitOps)

```bash
git add ai-react-frontend/k8s/configmap.yaml
git commit -m "feat: Switch backend to [BACKEND_NAME]"
git push origin main
```

## 🎯 Backend URLs Reference

| Backend | URL | Current Status |
|---------|-----|----------------|
| **Spring Boot** | `http://139.59.50.43:8080` | Available |
| **Django** | `http://152.42.157.69:8000` | Available |
| **Nest** | `http://139.59.53.144:3000` | **Currently Active** |

## 🔧 Frontend Service
- **URL**: `http://152.42.156.235:3000`
- **Config Endpoint**: `http://152.42.156.235:3000/env-config.js`
- **Health Endpoint**: `http://152.42.156.235:3000/health`

## ⚡ One-Line Command (After editing ConfigMap)
```bash
kubectl replace -f ai-react-frontend/k8s/configmap.yaml && kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev
```

## 🔍 Troubleshooting

### If Changes Don't Appear
```bash
# Force delete pod to recreate
kubectl delete pod -n ai-react-frontend-dev -l app=ai-react-frontend

# Check pod status
kubectl get pods -n ai-react-frontend-dev -l app=ai-react-frontend

# Check pod logs
kubectl logs -n ai-react-frontend-dev -l app=ai-react-frontend
```

## ✅ Success Indicators
- Pod status shows `Running` and `Ready 1/1`
- External endpoint returns correct `REACT_APP_BACKEND_URL`
- External endpoint shows correct `REACT_APP_CURRENT_BACKEND`
- No errors in pod logs

## ⏱️ Expected Timeline
- ConfigMap update: Immediate
- Pod restart: 10-30 seconds
- Configuration available: 30-60 seconds total

## 🔄 Complete Workflow Example

1. **Edit ConfigMap**: Change URLs in `ai-react-frontend/k8s/configmap.yaml`
2. **Apply**: `kubectl replace -f ai-react-frontend/k8s/configmap.yaml`
3. **Restart**: `kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev`
4. **Wait**: `kubectl rollout status deployment ai-react-frontend -n ai-react-frontend-dev`
5. **Test**: `curl http://152.42.156.235:3000/env-config.js`
6. **Commit**: `git add . && git commit -m "Switch to [backend]" && git push`
