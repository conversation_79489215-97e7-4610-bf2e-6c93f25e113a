apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: my-app-dev
  namespace: argocd
  labels:
    app.kubernetes.io/name: my-app-dev
    environment: dev
    app-type: springboot-backend
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: my-app-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: my-app/k8s
  destination:
    # Target cluster for dev/staging environments
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
    namespace: my-app-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 2
  info:
  - name: Description
    value: "My App - Development Environment"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Target Cluster
    value: "doks-target-cluster"
---
apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: my-app-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: my-app-project
    environment: dev
spec:
  description: "My App Project for GitOps deployment"
  sourceRepos:
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps.git'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps'
  destinations:
  # Allow deployment to target cluster
  - namespace: my-app-dev
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
  - namespace: my-app-staging
    server: https://0eae25c8-1244-4c33-89fb-5e03974780a6.k8s.ondigitalocean.com
  # Allow deployment to management cluster for production
  - namespace: my-app-production
    server: https://kubernetes.default.svc
  - namespace: argocd
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  - group: storage.k8s.io
    kind: StorageClass
  namespaceResourceWhitelist:
  - group: ''
    kind: ConfigMap
  - group: ''
    kind: Secret
  - group: ''
    kind: Service
  - group: ''
    kind: ServiceAccount
  - group: apps
    kind: Deployment
  - group: apps
    kind: ReplicaSet
  - group: ''
    kind: Pod
  - group: networking.k8s.io
    kind: Ingress
  - group: networking.k8s.io
    kind: NetworkPolicy
  roles:
  - name: admin
    description: Admin access to the project
    policies:
    - p, proj:my-app-project:admin, applications, *, my-app-project/*, allow
    - p, proj:my-app-project:admin, repositories, *, *, allow
    groups:
    - argocd-admins
