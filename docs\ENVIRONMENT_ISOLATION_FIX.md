# Environment Isolation Fix for GitOps Deployment Pipeline

## Problem Description

The GitOps deployment workflow was experiencing cross-environment contamination where placeholder replacement was affecting multiple environment overlays incorrectly:

### Issue Symptoms
1. **Dev deployment**: Placeholders in `overlays/dev/` files were correctly replaced with dynamic values
2. **Staging deployment**: Placeholders in `overlays/staging/` files were correctly replaced, BUT previously replaced values in `overlays/dev/` files were reverted back to placeholders
3. **Production deployment**: Placeholders in `overlays/production/` files were correctly replaced, BUT previously replaced values in both `overlays/dev/` and `overlays/staging/` files were reverted back to placeholders

### Expected Behavior
Only the target environment's overlay directory should have its placeholders replaced with dynamic values. Other environment overlays should remain unchanged.

## Root Cause Analysis

The issue was in the `process_payload.py` script's `process_manifest_directory` function:

### Original Problematic Code
```python
def process_manifest_directory(manifest_dir, mapping, environment=None):
    # Process base manifests - ❌ PROBLEM: Modifying template files
    base_dir = manifest_path / 'base'
    if base_dir.exists():
        for pattern in patterns:
            for file_path in base_dir.glob(pattern):
                replace_placeholders_in_file(file_path, mapping)  # ❌ Contaminating template
    
    # Process target environment overlay - ✅ This was correct
    overlay_dir = manifest_path / 'overlays' / environment
    # ... process overlay files
    
    # Process ArgoCD manifests - ❌ PROBLEM: Modifying template files
    argocd_dir = manifest_path / 'argocd'
    # ... process argocd files
    
    # Process components - ❌ PROBLEM: Modifying template files
    components_dir = manifest_path / 'components'
    # ... process component files
```

### Why This Caused Cross-Contamination
1. The `manifests/` directory serves as a **template** that gets copied to create project-specific deployments
2. When the script processed the template's `base`, `argocd`, and `components` directories, it was modifying the **template files themselves**
3. This meant that when the next environment deployment ran, it started with a template that already had values from the previous deployment instead of clean placeholders

## Solution Implementation

### Key Changes Made

1. **Added Template Detection**: Added an `is_template` parameter to distinguish between template directories and project-specific copies
2. **Environment Isolation**: For template directories, only process the target environment's overlay directory
3. **Full Processing for Projects**: For project-specific copies, process all directories as they are isolated copies

### Fixed Code Structure
```python
def process_manifest_directory(manifest_dir, mapping, environment=None, is_template=False):
    if is_template:
        # For template directories, ONLY process the target environment overlay
        # This prevents contamination of the template with environment-specific values
        overlay_dir = manifest_path / 'overlays' / environment
        # ... process only the target overlay
    else:
        # For project directories, process all directories as they are project-specific copies
        # Process base, overlays, argocd, and components
        # ... process all directories safely
```

### Template vs Project Directory Logic
- **Template Directory** (`manifests/`): Only process target environment overlay to prevent cross-contamination
- **Project Directory** (`deployments/project-id/`): Process all directories as they are isolated copies

## Files Modified

### Primary Fix
- `scripts/process_payload.py`:
  - Modified `process_manifest_directory()` function to add `is_template` parameter
  - Updated `create_processed_manifests()` to pass `is_template=False` for project copies
  - Updated main function to detect template directories and pass appropriate `is_template` value

## Verification

### Test Results
Created and ran `test-environment-isolation.py` which:
1. ✅ Tests deployment to dev, staging, and production environments sequentially
2. ✅ Verifies template integrity after each deployment
3. ✅ Confirms that template files retain placeholders after all deployments
4. ✅ All tests passed successfully

### Test Output Summary
```
DEV Deployment: ✅ PASS
DEV Template Integrity: ✅ PASS
STAGING Deployment: ✅ PASS
STAGING Template Integrity: ✅ PASS
PRODUCTION Deployment: ✅ PASS
PRODUCTION Template Integrity: ✅ PASS

🎉 ALL TESTS PASSED! Environment isolation is working correctly.
```

## How the Fix Works

### Before the Fix
1. Deploy to dev → Template files get contaminated with dev values
2. Deploy to staging → Template files get contaminated with staging values, dev files revert to placeholders
3. Deploy to production → Template files get contaminated with production values, dev and staging files revert to placeholders

### After the Fix
1. Deploy to dev → Only `manifests/overlays/dev/` files are processed, template remains clean
2. Deploy to staging → Only `manifests/overlays/staging/` files are processed, template remains clean
3. Deploy to production → Only `manifests/overlays/production/` files are processed, template remains clean

### Workflow Flow
1. **GitHub Actions** triggers deployment with environment-specific payload
2. **process_payload.py** detects it's working with template directory (`manifests/`)
3. **Template Processing**: Only processes the target environment's overlay directory
4. **Project Creation**: Copies template to `deployments/project-id/` and processes all directories in the copy
5. **Result**: Template remains clean, project gets fully processed manifests

## Benefits

1. **Environment Isolation**: Each environment deployment only affects its own overlay files
2. **Template Preservation**: Template files remain clean with placeholders for future deployments
3. **Backward Compatibility**: Existing project-specific processing continues to work as before
4. **Predictable Behavior**: Deployments are now deterministic and don't affect other environments

## Future Considerations

- This fix ensures that the template directory (`manifests/`) is never contaminated
- Project-specific directories under `deployments/` continue to be fully processed
- The solution is backward compatible with existing deployment workflows
- Consider adding additional validation to prevent accidental template modification in the future
