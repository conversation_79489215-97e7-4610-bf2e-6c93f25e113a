apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: donation-receipt-frontend-ingress
  labels:
    app: donation-receipt-frontend
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - dev.app.pheart.in
    secretName: donation-receipt-frontend-tls
  rules:
  - host: dev.app.pheart.in
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: donation-receipt-frontend-service-dev
            port:
              number: 3000
