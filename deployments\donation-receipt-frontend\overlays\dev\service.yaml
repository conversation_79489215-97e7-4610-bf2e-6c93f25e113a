apiVersion: v1
kind: Service
metadata:
  name: donation-receipt-frontend-service-dev
  labels:
    app: donation-receipt-frontend
    app.kubernetes.io/name: donation-receipt-frontend
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/part-of: donation-receipt-frontend
    app.kubernetes.io/version: "0b08fe33"
    app.kubernetes.io/managed-by: argocd
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: donation-receipt-frontend
