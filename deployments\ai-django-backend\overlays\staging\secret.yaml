apiVersion: v1
kind: Secret
metadata:
  name: ai-django-backend-secrets
  labels:
    app: ai-django-backend
    app.kubernetes.io/name: ai-django-backend
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: ai-django-backend
    app.kubernetes.io/version: "f4509a52"
    app.kubernetes.io/managed-by: argocd
    environment: staging
type: Opaque
data:
  # Staging Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  
  
  
  DATABASE_URL: ********************************************************************************************************************************************************************************************************************************
  

  

  # Essential Authentication Secrets
  JWT_SECRET: Y2VkMzU2NzJmNTU2YzBhZGU5M2RhNTAwZTdiNTc5YTliZjI1NDNmNjQ5OWMwODI0NjNkYmYxZmQ4Nzc2OGY5Mw==
  
  JWT_EXPIRES_IN: DYNAMIC_JWT_EXPIRES_IN_B64
  JWT_REFRESH_EXPIRES_IN: DYNAMIC_JWT_REFRESH_EXPIRES_IN_B64
  

  # Database Credentials (Staging)
  DB_USER: ZGphbmdvX3N0YWdpbmdfdXNlcg==
  DB_PASSWORD: QVZOU19hTFh1Wmp2c1JfR1doM3NvOW5x
  DB_HOST: cHJpdmF0ZS1kYmFhcy1kYi0xMDMyOTMyOC1kby11c2VyLTIzODE1NzQyLTAubS5kYi5vbmRpZ2l0YWxvY2Vhbi5jb20=
  DB_PORT: MjUwNjA=
  DB_NAME: ZGphbmdvX3N0YWdpbmdfZGI=
  DB_SSL_MODE: cmVxdWlyZQ==

  # SMTP Configuration (Staging)
  SMTP_USER: ********************************************
  SMTP_PASS: ********************************************************************************************

  # OAuth2 Configuration (Staging)
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=

  
  # Django-specific secrets
  SESSION_SECRET: M24hQCM5ZDhmN2c2aDVqNGszbDJtMW4wYjl2OGM3eDZ6NWE0czNkMmYxZzBoOWo4azdsNm01bjRiM3YyYzE=
  RATE_LIMIT_WINDOW_MS: OTAwMDAw
  RATE_LIMIT_MAX_REQUESTS: MTAw
  PASSWORD_RESET_TOKEN_EXPIRY: MzYwMA==
  EMAIL_VERIFICATION_TOKEN_EXPIRY: MzYwMA==
  

  

  # Staging-specific secrets
  # MONITORING_API_KEY: DYNAMIC_MONITORING_API_KEY_B64
  # EXTERNAL_API_KEY: DYNAMIC_EXTERNAL_API_KEY_B64
