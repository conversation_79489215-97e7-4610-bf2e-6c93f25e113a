apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: pure-heart-frontend-guest-dev

resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- ingress.yaml
# Note: secret.yaml is conditionally included based on APPLICATION_TYPE
# For react-frontend applications, secrets are handled differently

components:
- ../../components/common-labels
# Note: react-frontend component is conditionally included based on APPLICATION_TYPE

labels:
- pairs:
    app: pure-heart-frontend-guest
    app.kubernetes.io/name: pure-heart-frontend-guest
    app.kubernetes.io/part-of: pure-heart-frontend-guest
    app.kubernetes.io/component: react-frontend
    app.kubernetes.io/version: "4a061f0f"
    app.kubernetes.io/managed-by: argocd
    environment: dev
    source.repo: ChidhagniConsulting-Pure-Heart-Frontend-Guest
    source.branch: 9-merge

# Environment-specific configurations are now directly in the manifest files
# Database init container is now managed at the environment level via init-container-patch.yaml

patches:
- path: patch-image.yaml
  target:
    kind: Deployment
    name: pure-heart-frontend-guest
# Note: init-container-patch.yaml is conditionally included based on APPLICATION_TYPE
# For backend applications (springboot, django, nest), database init containers are applied

# Image transformations are applied after patches
# This allows dynamic tag updates without modifying the deployment directly

namePrefix: ""
nameSuffix: "-dev"
